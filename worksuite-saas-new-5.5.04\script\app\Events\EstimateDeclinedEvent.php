<?php

namespace App\Events;

use App\Models\Estimate;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EstimateDeclinedEvent
{

    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $estimate;

    public function __construct(Estimate $estimate)
    {
        $this->estimate = $estimate;
    }

}
