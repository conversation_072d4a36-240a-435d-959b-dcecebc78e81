a:31:{s:6:"_token";s:40:"P5wL5zlkBYsmdlYtHi5YR8cNOGvKzx30XjCpYlVh";s:3:"url";a:1:{s:8:"intended";s:108:"http://localhost/cc-worksuite/worksuite-saas-new-5.5.04/script/public/account/dashboard-advanced?tab=project";}s:9:"_previous";a:1:{s:3:"url";s:107:"http://localhost/cc-worksuite/worksuite-saas-new-5.5.04/script/public/account/dashboard-advanced?tab=ticket";}s:6:"_flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}s:20:"check_migrate_status";s:4:"Good";s:16:"current_latitude";N;s:17:"current_longitude";N;s:50:"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d";i:2;s:23:"laravel-device-tracking";a:1:{s:11:"current_md5";s:32:"027662272911895511a0eda064fe9f8d";}s:7:"company";O:18:"App\Models\Company":44:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"companies";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:95:{s:2:"id";i:1;s:10:"sub_domain";s:14:"demo.localhost";s:12:"company_name";s:12:"Demo Company";s:8:"app_name";s:12:"Demo Company";s:13:"company_email";s:17:"<EMAIL>";s:13:"company_phone";s:10:"**********";s:4:"logo";N;s:16:"login_background";N;s:7:"address";s:25:"Your Company address here";s:7:"website";s:21:"https://worksuite.biz";s:11:"currency_id";i:1;s:10:"package_id";i:1;s:12:"package_type";s:7:"monthly";s:8:"timezone";s:12:"Asia/Kolkata";s:11:"date_format";s:5:"d-m-Y";s:18:"date_picker_format";s:10:"dd-mm-yyyy";s:16:"year_starts_from";s:1:"1";s:13:"moment_format";s:10:"DD-MM-YYYY";s:11:"time_format";s:5:"h:i a";s:6:"locale";s:2:"en";s:8:"latitude";s:11:"26.91243360";s:9:"longitude";s:11:"75.78727090";s:17:"leaves_start_from";s:10:"year_start";s:12:"active_theme";s:7:"default";s:6:"status";s:6:"active";s:15:"last_updated_by";N;s:22:"currency_converter_key";N;s:14:"google_map_key";N;s:9:"task_self";s:3:"yes";s:13:"purchase_code";N;s:12:"license_type";N;s:15:"supported_until";N;s:23:"google_recaptcha_status";s:8:"deactive";s:26:"google_recaptcha_v2_status";s:8:"deactive";s:28:"google_recaptcha_v2_site_key";N;s:30:"google_recaptcha_v2_secret_key";N;s:26:"google_recaptcha_v3_status";s:8:"deactive";s:28:"google_recaptcha_v3_site_key";N;s:30:"google_recaptcha_v3_secret_key";N;s:9:"app_debug";i:0;s:13:"rounded_theme";i:1;s:17:"hide_cron_message";i:0;s:13:"system_update";i:1;s:21:"logo_background_color";s:7:"#ffffff";s:12:"header_color";s:7:"#1D82F5";s:11:"before_days";i:0;s:10:"after_days";i:0;s:11:"on_deadline";s:3:"yes";s:19:"default_task_status";i:1;s:17:"show_review_modal";i:1;s:15:"dashboard_clock";i:1;s:26:"ticket_form_google_captcha";i:0;s:24:"lead_form_google_captcha";i:0;s:16:"taskboard_length";i:10;s:19:"datatable_row_limit";i:10;s:13:"last_cron_run";N;s:7:"favicon";N;s:10:"auth_theme";s:5:"light";s:15:"auth_theme_text";s:4:"dark";s:10:"light_logo";N;s:18:"sidebar_logo_style";s:6:"square";s:14:"session_driver";s:4:"file";s:19:"allow_client_signup";i:0;s:28:"admin_client_signup_approval";i:0;s:18:"allowed_file_types";N;s:22:"google_calendar_status";s:8:"inactive";s:16:"google_client_id";N;s:20:"google_client_secret";N;s:35:"google_calendar_verification_status";s:12:"non_verified";s:9:"google_id";N;s:4:"name";N;s:5:"token";N;s:4:"hash";s:32:"9c6eabe1ea7d196a6ab33896fb8e475a";s:17:"allowed_file_size";i:10;s:20:"currency_key_version";s:4:"free";s:10:"created_at";s:19:"2025-08-26 07:59:46";s:10:"updated_at";s:19:"2025-09-07 10:05:23";s:10:"last_login";s:19:"2025-09-07 10:05:23";s:3:"rtl";i:0;s:9:"stripe_id";N;s:10:"card_brand";N;s:14:"card_last_four";N;s:13:"trial_ends_at";N;s:17:"licence_expire_on";N;s:18:"license_updated_at";N;s:23:"subscription_updated_at";N;s:8:"approved";i:1;s:11:"approved_by";N;s:22:"show_new_webhook_alert";i:0;s:7:"pm_type";N;s:12:"pm_last_four";N;s:24:"employee_can_export_data";i:1;s:7:"headers";s:797:"{
    "userAgent": "Symfony",
    "isMobile": false,
    "isTablet": false,
    "isDesktop": true,
    "isBot": false,
    "isChrome": false,
    "isFirefox": false,
    "isOpera": false,
    "isSafari": false,
    "isEdge": false,
    "isInApp": false,
    "isIE": false,
    "browserName": "",
    "browserFamily": "Unknown",
    "browserVersion": "",
    "browserVersionMajor": 0,
    "browserVersionMinor": 0,
    "browserVersionPatch": 0,
    "browserEngine": "Unknown",
    "platformName": "",
    "platformFamily": "Unknown",
    "platformVersion": "",
    "platformVersionMajor": 0,
    "platformVersionMinor": 0,
    "platformVersionPatch": 0,
    "isWindows": false,
    "isLinux": false,
    "isMac": false,
    "isAndroid": false,
    "deviceFamily": "Unknown",
    "deviceModel": ""
}";s:11:"register_ip";N;s:16:"location_details";N;}s:11:" * original";a:95:{s:2:"id";i:1;s:10:"sub_domain";s:14:"demo.localhost";s:12:"company_name";s:12:"Demo Company";s:8:"app_name";s:12:"Demo Company";s:13:"company_email";s:17:"<EMAIL>";s:13:"company_phone";s:10:"**********";s:4:"logo";N;s:16:"login_background";N;s:7:"address";s:25:"Your Company address here";s:7:"website";s:21:"https://worksuite.biz";s:11:"currency_id";i:1;s:10:"package_id";i:1;s:12:"package_type";s:7:"monthly";s:8:"timezone";s:12:"Asia/Kolkata";s:11:"date_format";s:5:"d-m-Y";s:18:"date_picker_format";s:10:"dd-mm-yyyy";s:16:"year_starts_from";s:1:"1";s:13:"moment_format";s:10:"DD-MM-YYYY";s:11:"time_format";s:5:"h:i a";s:6:"locale";s:2:"en";s:8:"latitude";s:11:"26.91243360";s:9:"longitude";s:11:"75.78727090";s:17:"leaves_start_from";s:10:"year_start";s:12:"active_theme";s:7:"default";s:6:"status";s:6:"active";s:15:"last_updated_by";N;s:22:"currency_converter_key";N;s:14:"google_map_key";N;s:9:"task_self";s:3:"yes";s:13:"purchase_code";N;s:12:"license_type";N;s:15:"supported_until";N;s:23:"google_recaptcha_status";s:8:"deactive";s:26:"google_recaptcha_v2_status";s:8:"deactive";s:28:"google_recaptcha_v2_site_key";N;s:30:"google_recaptcha_v2_secret_key";N;s:26:"google_recaptcha_v3_status";s:8:"deactive";s:28:"google_recaptcha_v3_site_key";N;s:30:"google_recaptcha_v3_secret_key";N;s:9:"app_debug";i:0;s:13:"rounded_theme";i:1;s:17:"hide_cron_message";i:0;s:13:"system_update";i:1;s:21:"logo_background_color";s:7:"#ffffff";s:12:"header_color";s:7:"#1D82F5";s:11:"before_days";i:0;s:10:"after_days";i:0;s:11:"on_deadline";s:3:"yes";s:19:"default_task_status";i:1;s:17:"show_review_modal";i:1;s:15:"dashboard_clock";i:1;s:26:"ticket_form_google_captcha";i:0;s:24:"lead_form_google_captcha";i:0;s:16:"taskboard_length";i:10;s:19:"datatable_row_limit";i:10;s:13:"last_cron_run";N;s:7:"favicon";N;s:10:"auth_theme";s:5:"light";s:15:"auth_theme_text";s:4:"dark";s:10:"light_logo";N;s:18:"sidebar_logo_style";s:6:"square";s:14:"session_driver";s:4:"file";s:19:"allow_client_signup";i:0;s:28:"admin_client_signup_approval";i:0;s:18:"allowed_file_types";N;s:22:"google_calendar_status";s:8:"inactive";s:16:"google_client_id";N;s:20:"google_client_secret";N;s:35:"google_calendar_verification_status";s:12:"non_verified";s:9:"google_id";N;s:4:"name";N;s:5:"token";N;s:4:"hash";s:32:"9c6eabe1ea7d196a6ab33896fb8e475a";s:17:"allowed_file_size";i:10;s:20:"currency_key_version";s:4:"free";s:10:"created_at";s:19:"2025-08-26 07:59:46";s:10:"updated_at";s:19:"2025-09-07 10:05:23";s:10:"last_login";s:19:"2025-09-07 10:05:23";s:3:"rtl";i:0;s:9:"stripe_id";N;s:10:"card_brand";N;s:14:"card_last_four";N;s:13:"trial_ends_at";N;s:17:"licence_expire_on";N;s:18:"license_updated_at";N;s:23:"subscription_updated_at";N;s:8:"approved";i:1;s:11:"approved_by";N;s:22:"show_new_webhook_alert";i:0;s:7:"pm_type";N;s:12:"pm_last_four";N;s:24:"employee_can_export_data";i:1;s:7:"headers";s:797:"{
    "userAgent": "Symfony",
    "isMobile": false,
    "isTablet": false,
    "isDesktop": true,
    "isBot": false,
    "isChrome": false,
    "isFirefox": false,
    "isOpera": false,
    "isSafari": false,
    "isEdge": false,
    "isInApp": false,
    "isIE": false,
    "browserName": "",
    "browserFamily": "Unknown",
    "browserVersion": "",
    "browserVersionMajor": 0,
    "browserVersionMinor": 0,
    "browserVersionPatch": 0,
    "browserEngine": "Unknown",
    "platformName": "",
    "platformFamily": "Unknown",
    "platformVersion": "",
    "platformVersionMajor": 0,
    "platformVersionMinor": 0,
    "platformVersionPatch": 0,
    "isWindows": false,
    "isLinux": false,
    "isMac": false,
    "isAndroid": false,
    "deviceFamily": "Unknown",
    "deviceModel": ""
}";s:11:"register_ip";N;s:16:"location_details";N;}s:10:" * changes";a:2:{s:10:"updated_at";s:19:"2025-09-07 10:05:23";s:10:"last_login";s:19:"2025-09-07 10:05:23";}s:8:" * casts";a:1:{s:22:"google_calendar_status";s:6:"string";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:4:{i:0;s:8:"logo_url";i:1;s:20:"login_background_url";i:2;s:18:"moment_date_format";i:3;s:11:"favicon_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:7:"package";O:29:"App\Models\SuperAdmin\Package":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"packages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:30:{s:2:"id";i:1;s:11:"currency_id";i:1;s:4:"name";s:7:"Default";s:5:"price";N;s:11:"description";s:43:"Its a default package and cannot be deleted";s:16:"max_storage_size";i:0;s:13:"max_file_size";i:0;s:12:"annual_price";d:0;s:13:"monthly_price";d:0;s:13:"billing_cycle";i:0;s:13:"max_employees";i:20;s:4:"sort";s:1:"1";s:17:"module_in_package";s:251:"["clients","employees","projects","attendance","tasks","estimates","invoices","payments","timelogs","tickets","events","notices","leaves","leads","holidays","products","expenses","contracts","reports","orders","knowledgebase","bankaccount","messages"]";s:21:"stripe_annual_plan_id";N;s:22:"stripe_monthly_plan_id";N;s:23:"razorpay_annual_plan_id";N;s:24:"razorpay_monthly_plan_id";N;s:7:"default";s:3:"yes";s:24:"paystack_monthly_plan_id";N;s:23:"paystack_annual_plan_id";N;s:10:"is_private";i:0;s:12:"storage_unit";s:2:"mb";s:14:"is_recommended";i:0;s:7:"is_free";i:1;s:13:"is_auto_renew";i:0;s:14:"monthly_status";s:1:"1";s:13:"annual_status";s:1:"1";s:7:"package";N;s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 07:59:47";}s:11:" * original";a:30:{s:2:"id";i:1;s:11:"currency_id";i:1;s:4:"name";s:7:"Default";s:5:"price";N;s:11:"description";s:43:"Its a default package and cannot be deleted";s:16:"max_storage_size";i:0;s:13:"max_file_size";i:0;s:12:"annual_price";d:0;s:13:"monthly_price";d:0;s:13:"billing_cycle";i:0;s:13:"max_employees";i:20;s:4:"sort";s:1:"1";s:17:"module_in_package";s:251:"["clients","employees","projects","attendance","tasks","estimates","invoices","payments","timelogs","tickets","events","notices","leaves","leads","holidays","products","expenses","contracts","reports","orders","knowledgebase","bankaccount","messages"]";s:21:"stripe_annual_plan_id";N;s:22:"stripe_monthly_plan_id";N;s:23:"razorpay_annual_plan_id";N;s:24:"razorpay_monthly_plan_id";N;s:7:"default";s:3:"yes";s:24:"paystack_monthly_plan_id";N;s:23:"paystack_annual_plan_id";N;s:10:"is_private";i:0;s:12:"storage_unit";s:2:"mb";s:14:"is_recommended";i:0;s:7:"is_free";i:1;s:13:"is_auto_renew";i:0;s:14:"monthly_status";s:1:"1";s:13:"annual_status";s:1:"1";s:7:"package";N;s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 07:59:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:22:"formatted_annual_price";i:1;s:23:"formatted_monthly_price";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:5:"dates";a:3:{i:0;s:10:"last_login";i:1;s:23:"subscription_updated_at";i:2;s:17:"licence_expire_on";}s:5:"model";N;s:13:"custom_fields";N;s:18:"custom_fields_data";N;s:17:"customerIpAddress";N;s:24:"estimationBillingAddress";a:0:{}s:13:"collectTaxIds";b:0;s:8:"couponId";N;s:15:"promotionCodeId";N;s:19:"allowPromotionCodes";b:0;}s:4:"user";O:15:"App\Models\User":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:2:{i:0;s:10:"session:id";i:1;s:13:"clientContact";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:36:{s:2:"id";i:2;s:10:"company_id";i:1;s:12:"user_auth_id";i:2;s:13:"is_superadmin";i:0;s:4:"name";s:5:"Admin";s:5:"email";s:17:"<EMAIL>";s:5:"image";N;s:17:"country_phonecode";N;s:6:"mobile";N;s:6:"gender";s:4:"male";s:10:"salutation";N;s:6:"locale";s:2:"en";s:6:"status";s:6:"active";s:5:"login";s:6:"enable";s:19:"onesignal_player_id";N;s:10:"last_login";s:19:"2025-09-07 10:05:23";s:19:"email_notifications";i:1;s:10:"country_id";N;s:10:"dark_theme";i:0;s:3:"rtl";i:0;s:14:"admin_approval";i:1;s:15:"permission_sync";i:1;s:22:"google_calendar_status";i:1;s:10:"created_at";s:19:"2025-08-26 08:05:39";s:10:"updated_at";s:19:"2025-09-07 10:05:23";s:22:"customised_permissions";i:0;s:9:"stripe_id";N;s:7:"pm_type";N;s:12:"pm_last_four";N;s:13:"trial_ends_at";N;s:7:"headers";s:924:"{
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "isMobile": false,
    "isTablet": false,
    "isDesktop": true,
    "isBot": false,
    "isChrome": true,
    "isFirefox": false,
    "isOpera": false,
    "isSafari": false,
    "isEdge": false,
    "isInApp": false,
    "isIE": false,
    "browserName": "Chrome 139",
    "browserFamily": "Chrome",
    "browserVersion": "139",
    "browserVersionMajor": 139,
    "browserVersionMinor": 0,
    "browserVersionPatch": 0,
    "browserEngine": "Blink",
    "platformName": "Windows 10",
    "platformFamily": "Windows",
    "platformVersion": "10",
    "platformVersionMajor": 10,
    "platformVersionMinor": 0,
    "platformVersionPatch": 0,
    "isWindows": true,
    "isLinux": false,
    "isMac": false,
    "isAndroid": false,
    "deviceFamily": "Unknown",
    "deviceModel": ""
}";s:11:"register_ip";N;s:16:"location_details";N;s:13:"inactive_date";N;s:17:"is_client_contact";N;s:16:"telegram_user_id";N;}s:11:" * original";a:36:{s:2:"id";i:2;s:10:"company_id";i:1;s:12:"user_auth_id";i:2;s:13:"is_superadmin";i:0;s:4:"name";s:5:"Admin";s:5:"email";s:17:"<EMAIL>";s:5:"image";N;s:17:"country_phonecode";N;s:6:"mobile";N;s:6:"gender";s:4:"male";s:10:"salutation";N;s:6:"locale";s:2:"en";s:6:"status";s:6:"active";s:5:"login";s:6:"enable";s:19:"onesignal_player_id";N;s:10:"last_login";s:19:"2025-09-07 10:05:23";s:19:"email_notifications";i:1;s:10:"country_id";N;s:10:"dark_theme";i:0;s:3:"rtl";i:0;s:14:"admin_approval";i:1;s:15:"permission_sync";i:1;s:22:"google_calendar_status";i:1;s:10:"created_at";s:19:"2025-08-26 08:05:39";s:10:"updated_at";s:19:"2025-09-07 10:05:23";s:22:"customised_permissions";i:0;s:9:"stripe_id";N;s:7:"pm_type";N;s:12:"pm_last_four";N;s:13:"trial_ends_at";N;s:7:"headers";s:924:"{
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "isMobile": false,
    "isTablet": false,
    "isDesktop": true,
    "isBot": false,
    "isChrome": true,
    "isFirefox": false,
    "isOpera": false,
    "isSafari": false,
    "isEdge": false,
    "isInApp": false,
    "isIE": false,
    "browserName": "Chrome 139",
    "browserFamily": "Chrome",
    "browserVersion": "139",
    "browserVersionMajor": 139,
    "browserVersionMinor": 0,
    "browserVersionPatch": 0,
    "browserEngine": "Blink",
    "platformName": "Windows 10",
    "platformFamily": "Windows",
    "platformVersion": "10",
    "platformVersionMajor": 10,
    "platformVersionMinor": 0,
    "platformVersionPatch": 0,
    "isWindows": true,
    "isLinux": false,
    "isMac": false,
    "isAndroid": false,
    "deviceFamily": "Unknown",
    "deviceModel": ""
}";s:11:"register_ip";N;s:16:"location_details";N;s:13:"inactive_date";N;s:17:"is_client_contact";N;s:16:"telegram_user_id";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:5:{s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";s:10:"last_login";s:8:"datetime";s:22:"two_factor_expires_at	";s:5:"array";s:10:"salutation";s:20:"App\Enums\Salutation";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:4:{i:0;s:9:"image_url";i:1;s:7:"modules";i:2;s:21:"mobile_with_phonecode";i:3;s:15:"name_salutation";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:6:{s:7:"session";N;s:13:"clientContact";N;s:5:"roles";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:15:"App\Models\Role":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"roles";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:1;s:10:"company_id";i:1;s:4:"name";s:5:"admin";s:12:"display_name";s:17:"App Administrator";s:11:"description";s:49:"Admin is allowed to manage everything of the app.";s:10:"created_at";s:19:"2025-08-26 07:59:46";s:10:"updated_at";s:19:"2025-08-26 07:59:46";}s:11:" * original";a:9:{s:2:"id";i:1;s:10:"company_id";i:1;s:4:"name";s:5:"admin";s:12:"display_name";s:17:"App Administrator";s:11:"description";s:49:"Admin is allowed to manage everything of the app.";s:10:"created_at";s:19:"2025-08-26 07:59:46";s:10:"updated_at";s:19:"2025-08-26 07:59:46";s:13:"pivot_user_id";i:2;s:13:"pivot_role_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"role_user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:7:"user_id";i:2;s:7:"role_id";i:1;}s:11:" * original";a:2:{s:7:"user_id";i:2;s:7:"role_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:370;s:13:" * foreignKey";s:7:"user_id";s:13:" * relatedKey";s:7:"role_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:12:"display_name";i:2;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:15:"App\Models\Role":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"roles";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:2;s:10:"company_id";i:1;s:4:"name";s:8:"employee";s:12:"display_name";s:8:"Employee";s:11:"description";s:52:"Employee can see tasks and projects assigned to him.";s:10:"created_at";s:19:"2025-08-26 07:59:47";s:10:"updated_at";s:19:"2025-08-26 07:59:47";}s:11:" * original";a:9:{s:2:"id";i:2;s:10:"company_id";i:1;s:4:"name";s:8:"employee";s:12:"display_name";s:8:"Employee";s:11:"description";s:52:"Employee can see tasks and projects assigned to him.";s:10:"created_at";s:19:"2025-08-26 07:59:47";s:10:"updated_at";s:19:"2025-08-26 07:59:47";s:13:"pivot_user_id";i:2;s:13:"pivot_role_id";i:2;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"role_user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:7:"user_id";i:2;s:7:"role_id";i:2;}s:11:" * original";a:2:{s:7:"user_id";i:2;s:7:"role_id";i:2;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:370;s:13:" * foreignKey";s:7:"user_id";s:13:" * relatedKey";s:7:"role_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:12:"display_name";i:2;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:6:"sticky";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:14:"employeeDetail";O:26:"App\Models\EmployeeDetails":38:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"employee_details";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:10:"company:id";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:30:{s:2:"id";i:1;s:10:"company_id";i:1;s:7:"user_id";i:2;s:11:"employee_id";s:5:"EMP-1";s:7:"address";N;s:11:"hourly_rate";N;s:14:"slack_username";N;s:13:"department_id";N;s:14:"designation_id";N;s:12:"joining_date";s:19:"2025-08-26 13:05:39";s:9:"last_date";N;s:8:"added_by";N;s:15:"last_updated_by";N;s:19:"attendance_reminder";N;s:13:"date_of_birth";N;s:13:"calendar_view";N;s:8:"about_me";N;s:12:"reporting_to";N;s:17:"contract_end_date";N;s:19:"internship_end_date";N;s:15:"employment_type";N;s:25:"marriage_anniversary_date";N;s:14:"marital_status";s:6:"single";s:22:"notice_period_end_date";N;s:24:"notice_period_start_date";N;s:18:"probation_end_date";N;s:10:"created_at";s:19:"2025-08-26 08:05:39";s:10:"updated_at";s:19:"2025-08-26 08:05:39";s:18:"company_address_id";N;s:20:"overtime_hourly_rate";N;}s:11:" * original";a:30:{s:2:"id";i:1;s:10:"company_id";i:1;s:7:"user_id";i:2;s:11:"employee_id";s:5:"EMP-1";s:7:"address";N;s:11:"hourly_rate";N;s:14:"slack_username";N;s:13:"department_id";N;s:14:"designation_id";N;s:12:"joining_date";s:19:"2025-08-26 13:05:39";s:9:"last_date";N;s:8:"added_by";N;s:15:"last_updated_by";N;s:19:"attendance_reminder";N;s:13:"date_of_birth";N;s:13:"calendar_view";N;s:8:"about_me";N;s:12:"reporting_to";N;s:17:"contract_end_date";N;s:19:"internship_end_date";N;s:15:"employment_type";N;s:25:"marriage_anniversary_date";N;s:14:"marital_status";s:6:"single";s:22:"notice_period_end_date";N;s:24:"notice_period_start_date";N;s:18:"probation_end_date";N;s:10:"created_at";s:19:"2025-08-26 08:05:39";s:10:"updated_at";s:19:"2025-08-26 08:05:39";s:18:"company_address_id";N;s:20:"overtime_hourly_rate";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:5:{s:12:"joining_date";s:8:"datetime";s:9:"last_date";s:8:"datetime";s:13:"date_of_birth";s:8:"datetime";s:14:"calendar_view	";s:5:"array";s:14:"marital_status";s:23:"App\Enums\MaritalStatus";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:17:"upcoming_birthday";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:7:"company";O:18:"App\Models\Company":44:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"companies";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:1:{s:2:"id";i:1;}s:11:" * original";a:1:{s:2:"id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:22:"google_calendar_status";s:6:"string";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:4:{i:0;s:8:"logo_url";i:1;s:20:"login_background_url";i:2;s:18:"moment_date_format";i:3;s:11:"favicon_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:5:"dates";a:3:{i:0;s:10:"last_login";i:1;s:23:"subscription_updated_at";i:2;s:17:"licence_expire_on";}s:5:"model";N;s:13:"custom_fields";N;s:18:"custom_fields_data";N;s:17:"customerIpAddress";N;s:24:"estimationBillingAddress";a:0:{}s:13:"collectTaxIds";b:0;s:8:"couponId";N;s:15:"promotionCodeId";N;s:19:"allowPromotionCodes";b:0;}s:11:"designation";N;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}s:5:"model";N;s:13:"custom_fields";N;s:18:"custom_fields_data";N;}s:4:"role";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:19:"App\Models\RoleUser":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"role_user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:7:"user_id";i:2;s:7:"role_id";i:1;}s:11:" * original";a:2:{s:7:"user_id";i:2;s:7:"role_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}i:1;O:19:"App\Models\RoleUser":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"role_user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:7:"user_id";i:2;s:7:"role_id";i:2;}s:11:" * original";a:2:{s:7:"user_id";i:2;s:7:"role_id";i:2;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:4:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:7:"headers";i:3;s:16:"location_details";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:5:"dates";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:10:"last_login";}}s:12:"gdpr_setting";O:22:"App\Models\GdprSetting":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"gdpr_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:17:{s:2:"id";i:1;s:11:"enable_gdpr";i:0;s:18:"show_customer_area";i:0;s:20:"show_customer_footer";i:0;s:21:"top_information_block";N;s:13:"enable_export";i:0;s:12:"data_removal";i:0;s:24:"lead_removal_public_form";i:0;s:21:"terms_customer_footer";i:0;s:5:"terms";N;s:6:"policy";N;s:16:"public_lead_edit";i:0;s:16:"consent_customer";i:0;s:13:"consent_leads";i:0;s:13:"consent_block";N;s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 07:59:44";}s:11:" * original";a:17:{s:2:"id";i:1;s:11:"enable_gdpr";i:0;s:18:"show_customer_area";i:0;s:20:"show_customer_footer";i:0;s:21:"top_information_block";N;s:13:"enable_export";i:0;s:12:"data_removal";i:0;s:24:"lead_removal_public_form";i:0;s:21:"terms_customer_footer";i:0;s:5:"terms";N;s:6:"policy";N;s:16:"public_lead_edit";i:0;s:16:"consent_customer";i:0;s:13:"consent_leads";i:0;s:13:"consent_block";N;s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 07:59:44";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}s:16:"superadmin_theme";O:23:"App\Models\ThemeSetting":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"theme_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:14:{s:2:"id";i:1;s:10:"company_id";N;s:5:"panel";s:10:"superadmin";s:12:"header_color";s:7:"#ED4040";s:13:"sidebar_color";s:7:"#292929";s:18:"sidebar_text_color";s:7:"#cbcbcb";s:10:"link_color";s:7:"#ffffff";s:8:"user_css";N;s:13:"sidebar_theme";s:4:"dark";s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 08:10:58";s:20:"enable_rounded_theme";i:0;s:16:"login_background";N;s:27:"restrict_admin_theme_change";i:0;}s:11:" * original";a:14:{s:2:"id";i:1;s:10:"company_id";N;s:5:"panel";s:10:"superadmin";s:12:"header_color";s:7:"#ED4040";s:13:"sidebar_color";s:7:"#292929";s:18:"sidebar_text_color";s:7:"#cbcbcb";s:10:"link_color";s:7:"#ffffff";s:8:"user_css";N;s:13:"sidebar_theme";s:4:"dark";s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 08:10:58";s:20:"enable_rounded_theme";i:0;s:16:"login_background";N;s:27:"restrict_admin_theme_change";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}s:11:"admin_theme";O:23:"App\Models\ThemeSetting":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"theme_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:14:{s:2:"id";i:2;s:10:"company_id";i:1;s:5:"panel";s:5:"admin";s:12:"header_color";s:7:"#ed4040";s:13:"sidebar_color";s:7:"#292929";s:18:"sidebar_text_color";s:7:"#cbcbcb";s:10:"link_color";s:7:"#ffffff";s:8:"user_css";N;s:13:"sidebar_theme";s:4:"dark";s:10:"created_at";N;s:10:"updated_at";s:19:"2025-08-26 08:10:58";s:20:"enable_rounded_theme";i:0;s:16:"login_background";N;s:27:"restrict_admin_theme_change";i:0;}s:11:" * original";a:14:{s:2:"id";i:2;s:10:"company_id";i:1;s:5:"panel";s:5:"admin";s:12:"header_color";s:7:"#ed4040";s:13:"sidebar_color";s:7:"#292929";s:18:"sidebar_text_color";s:7:"#cbcbcb";s:10:"link_color";s:7:"#ffffff";s:8:"user_css";N;s:13:"sidebar_theme";s:4:"dark";s:10:"created_at";N;s:10:"updated_at";s:19:"2025-08-26 08:10:58";s:20:"enable_rounded_theme";i:0;s:16:"login_background";N;s:27:"restrict_admin_theme_change";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}s:15:"invoice_setting";O:25:"App\Models\InvoiceSetting":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"invoice_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:48:{s:2:"id";i:1;s:10:"company_id";i:1;s:14:"invoice_prefix";s:3:"INV";s:24:"invoice_number_separator";s:1:"#";s:13:"invoice_digit";i:3;s:15:"estimate_prefix";s:3:"EST";s:25:"estimate_number_separator";s:1:"#";s:14:"estimate_digit";i:3;s:18:"credit_note_prefix";s:2:"CN";s:28:"credit_note_number_separator";s:1:"#";s:17:"credit_note_digit";i:3;s:15:"contract_prefix";s:4:"CONT";s:25:"contract_number_separator";s:1:"#";s:23:"estimate_request_prefix";s:5:"ESTRQ";s:33:"estimate_request_number_separator";s:1:"#";s:22:"estimate_request_digit";i:3;s:14:"contract_digit";i:3;s:12:"order_prefix";s:3:"ODR";s:22:"order_number_separator";s:1:"#";s:11:"order_digit";i:3;s:15:"proposal_prefix";s:8:"Proposal";s:25:"proposal_number_separator";s:1:"#";s:14:"proposal_digit";i:3;s:8:"template";s:9:"invoice-5";s:9:"due_after";i:15;s:13:"invoice_terms";s:28:"Thank you for your business.";s:10:"other_info";N;s:14:"estimate_terms";N;s:10:"gst_number";N;s:8:"show_gst";s:2:"no";s:4:"logo";N;s:17:"hsn_sac_code_show";i:0;s:6:"locale";s:2:"en";s:13:"send_reminder";i:0;s:8:"reminder";N;s:19:"send_reminder_after";i:0;s:19:"tax_calculation_msg";i:0;s:11:"show_status";i:1;s:20:"authorised_signatory";i:0;s:30:"authorised_signatory_signature";N;s:12:"show_project";i:0;s:16:"show_client_name";s:3:"yes";s:17:"show_client_email";s:3:"yes";s:17:"show_client_phone";s:3:"yes";s:27:"show_client_company_address";s:3:"yes";s:24:"show_client_company_name";s:3:"yes";s:10:"created_at";s:19:"2025-08-26 07:59:47";s:10:"updated_at";s:19:"2025-08-26 07:59:47";}s:11:" * original";a:48:{s:2:"id";i:1;s:10:"company_id";i:1;s:14:"invoice_prefix";s:3:"INV";s:24:"invoice_number_separator";s:1:"#";s:13:"invoice_digit";i:3;s:15:"estimate_prefix";s:3:"EST";s:25:"estimate_number_separator";s:1:"#";s:14:"estimate_digit";i:3;s:18:"credit_note_prefix";s:2:"CN";s:28:"credit_note_number_separator";s:1:"#";s:17:"credit_note_digit";i:3;s:15:"contract_prefix";s:4:"CONT";s:25:"contract_number_separator";s:1:"#";s:23:"estimate_request_prefix";s:5:"ESTRQ";s:33:"estimate_request_number_separator";s:1:"#";s:22:"estimate_request_digit";i:3;s:14:"contract_digit";i:3;s:12:"order_prefix";s:3:"ODR";s:22:"order_number_separator";s:1:"#";s:11:"order_digit";i:3;s:15:"proposal_prefix";s:8:"Proposal";s:25:"proposal_number_separator";s:1:"#";s:14:"proposal_digit";i:3;s:8:"template";s:9:"invoice-5";s:9:"due_after";i:15;s:13:"invoice_terms";s:28:"Thank you for your business.";s:10:"other_info";N;s:14:"estimate_terms";N;s:10:"gst_number";N;s:8:"show_gst";s:2:"no";s:4:"logo";N;s:17:"hsn_sac_code_show";i:0;s:6:"locale";s:2:"en";s:13:"send_reminder";i:0;s:8:"reminder";N;s:19:"send_reminder_after";i:0;s:19:"tax_calculation_msg";i:0;s:11:"show_status";i:1;s:20:"authorised_signatory";i:0;s:30:"authorised_signatory_signature";N;s:12:"show_project";i:0;s:16:"show_client_name";s:3:"yes";s:17:"show_client_email";s:3:"yes";s:17:"show_client_phone";s:3:"yes";s:27:"show_client_company_address";s:3:"yes";s:24:"show_client_company_name";s:3:"yes";s:10:"created_at";s:19:"2025-08-26 07:59:47";s:10:"updated_at";s:19:"2025-08-26 07:59:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:3:{i:0;s:8:"logo_url";i:1;s:34:"authorised_signatory_signature_url";i:2;s:15:"is_chinese_lang";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}s:19:"custom_link_setting";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:14:"user_companies";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:15:"App\Models\User":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:2:{i:0;s:10:"session:id";i:1;s:13:"clientContact";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:2;s:10:"company_id";i:1;s:6:"status";s:6:"active";}s:11:" * original";a:3:{s:2:"id";i:2;s:10:"company_id";i:1;s:6:"status";s:6:"active";}s:10:" * changes";a:0:{}s:8:" * casts";a:5:{s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";s:10:"last_login";s:8:"datetime";s:22:"two_factor_expires_at	";s:5:"array";s:10:"salutation";s:20:"App\Enums\Salutation";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:4:{i:0;s:9:"image_url";i:1;s:7:"modules";i:2;s:21:"mobile_with_phonecode";i:3;s:15:"name_salutation";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:3:{s:7:"session";N;s:13:"clientContact";N;s:7:"company";O:18:"App\Models\Company":44:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"companies";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:95:{s:2:"id";i:1;s:10:"sub_domain";s:14:"demo.localhost";s:12:"company_name";s:12:"Demo Company";s:8:"app_name";s:12:"Demo Company";s:13:"company_email";s:17:"<EMAIL>";s:13:"company_phone";s:10:"**********";s:4:"logo";N;s:16:"login_background";N;s:7:"address";s:25:"Your Company address here";s:7:"website";s:21:"https://worksuite.biz";s:11:"currency_id";i:1;s:10:"package_id";i:1;s:12:"package_type";s:7:"monthly";s:8:"timezone";s:12:"Asia/Kolkata";s:11:"date_format";s:5:"d-m-Y";s:18:"date_picker_format";s:10:"dd-mm-yyyy";s:16:"year_starts_from";s:1:"1";s:13:"moment_format";s:10:"DD-MM-YYYY";s:11:"time_format";s:5:"h:i a";s:6:"locale";s:2:"en";s:8:"latitude";s:11:"26.91243360";s:9:"longitude";s:11:"75.78727090";s:17:"leaves_start_from";s:10:"year_start";s:12:"active_theme";s:7:"default";s:6:"status";s:6:"active";s:15:"last_updated_by";N;s:22:"currency_converter_key";N;s:14:"google_map_key";N;s:9:"task_self";s:3:"yes";s:13:"purchase_code";N;s:12:"license_type";N;s:15:"supported_until";N;s:23:"google_recaptcha_status";s:8:"deactive";s:26:"google_recaptcha_v2_status";s:8:"deactive";s:28:"google_recaptcha_v2_site_key";N;s:30:"google_recaptcha_v2_secret_key";N;s:26:"google_recaptcha_v3_status";s:8:"deactive";s:28:"google_recaptcha_v3_site_key";N;s:30:"google_recaptcha_v3_secret_key";N;s:9:"app_debug";i:0;s:13:"rounded_theme";i:1;s:17:"hide_cron_message";i:0;s:13:"system_update";i:1;s:21:"logo_background_color";s:7:"#ffffff";s:12:"header_color";s:7:"#1D82F5";s:11:"before_days";i:0;s:10:"after_days";i:0;s:11:"on_deadline";s:3:"yes";s:19:"default_task_status";i:1;s:17:"show_review_modal";i:1;s:15:"dashboard_clock";i:1;s:26:"ticket_form_google_captcha";i:0;s:24:"lead_form_google_captcha";i:0;s:16:"taskboard_length";i:10;s:19:"datatable_row_limit";i:10;s:13:"last_cron_run";N;s:7:"favicon";N;s:10:"auth_theme";s:5:"light";s:15:"auth_theme_text";s:4:"dark";s:10:"light_logo";N;s:18:"sidebar_logo_style";s:6:"square";s:14:"session_driver";s:4:"file";s:19:"allow_client_signup";i:0;s:28:"admin_client_signup_approval";i:0;s:18:"allowed_file_types";N;s:22:"google_calendar_status";s:8:"inactive";s:16:"google_client_id";N;s:20:"google_client_secret";N;s:35:"google_calendar_verification_status";s:12:"non_verified";s:9:"google_id";N;s:4:"name";N;s:5:"token";N;s:4:"hash";s:32:"9c6eabe1ea7d196a6ab33896fb8e475a";s:17:"allowed_file_size";i:10;s:20:"currency_key_version";s:4:"free";s:10:"created_at";s:19:"2025-08-26 07:59:46";s:10:"updated_at";s:19:"2025-09-07 10:05:23";s:10:"last_login";s:19:"2025-09-07 10:05:23";s:3:"rtl";i:0;s:9:"stripe_id";N;s:10:"card_brand";N;s:14:"card_last_four";N;s:13:"trial_ends_at";N;s:17:"licence_expire_on";N;s:18:"license_updated_at";N;s:23:"subscription_updated_at";N;s:8:"approved";i:1;s:11:"approved_by";N;s:22:"show_new_webhook_alert";i:0;s:7:"pm_type";N;s:12:"pm_last_four";N;s:24:"employee_can_export_data";i:1;s:7:"headers";s:797:"{
    "userAgent": "Symfony",
    "isMobile": false,
    "isTablet": false,
    "isDesktop": true,
    "isBot": false,
    "isChrome": false,
    "isFirefox": false,
    "isOpera": false,
    "isSafari": false,
    "isEdge": false,
    "isInApp": false,
    "isIE": false,
    "browserName": "",
    "browserFamily": "Unknown",
    "browserVersion": "",
    "browserVersionMajor": 0,
    "browserVersionMinor": 0,
    "browserVersionPatch": 0,
    "browserEngine": "Unknown",
    "platformName": "",
    "platformFamily": "Unknown",
    "platformVersion": "",
    "platformVersionMajor": 0,
    "platformVersionMinor": 0,
    "platformVersionPatch": 0,
    "isWindows": false,
    "isLinux": false,
    "isMac": false,
    "isAndroid": false,
    "deviceFamily": "Unknown",
    "deviceModel": ""
}";s:11:"register_ip";N;s:16:"location_details";N;}s:11:" * original";a:95:{s:2:"id";i:1;s:10:"sub_domain";s:14:"demo.localhost";s:12:"company_name";s:12:"Demo Company";s:8:"app_name";s:12:"Demo Company";s:13:"company_email";s:17:"<EMAIL>";s:13:"company_phone";s:10:"**********";s:4:"logo";N;s:16:"login_background";N;s:7:"address";s:25:"Your Company address here";s:7:"website";s:21:"https://worksuite.biz";s:11:"currency_id";i:1;s:10:"package_id";i:1;s:12:"package_type";s:7:"monthly";s:8:"timezone";s:12:"Asia/Kolkata";s:11:"date_format";s:5:"d-m-Y";s:18:"date_picker_format";s:10:"dd-mm-yyyy";s:16:"year_starts_from";s:1:"1";s:13:"moment_format";s:10:"DD-MM-YYYY";s:11:"time_format";s:5:"h:i a";s:6:"locale";s:2:"en";s:8:"latitude";s:11:"26.91243360";s:9:"longitude";s:11:"75.78727090";s:17:"leaves_start_from";s:10:"year_start";s:12:"active_theme";s:7:"default";s:6:"status";s:6:"active";s:15:"last_updated_by";N;s:22:"currency_converter_key";N;s:14:"google_map_key";N;s:9:"task_self";s:3:"yes";s:13:"purchase_code";N;s:12:"license_type";N;s:15:"supported_until";N;s:23:"google_recaptcha_status";s:8:"deactive";s:26:"google_recaptcha_v2_status";s:8:"deactive";s:28:"google_recaptcha_v2_site_key";N;s:30:"google_recaptcha_v2_secret_key";N;s:26:"google_recaptcha_v3_status";s:8:"deactive";s:28:"google_recaptcha_v3_site_key";N;s:30:"google_recaptcha_v3_secret_key";N;s:9:"app_debug";i:0;s:13:"rounded_theme";i:1;s:17:"hide_cron_message";i:0;s:13:"system_update";i:1;s:21:"logo_background_color";s:7:"#ffffff";s:12:"header_color";s:7:"#1D82F5";s:11:"before_days";i:0;s:10:"after_days";i:0;s:11:"on_deadline";s:3:"yes";s:19:"default_task_status";i:1;s:17:"show_review_modal";i:1;s:15:"dashboard_clock";i:1;s:26:"ticket_form_google_captcha";i:0;s:24:"lead_form_google_captcha";i:0;s:16:"taskboard_length";i:10;s:19:"datatable_row_limit";i:10;s:13:"last_cron_run";N;s:7:"favicon";N;s:10:"auth_theme";s:5:"light";s:15:"auth_theme_text";s:4:"dark";s:10:"light_logo";N;s:18:"sidebar_logo_style";s:6:"square";s:14:"session_driver";s:4:"file";s:19:"allow_client_signup";i:0;s:28:"admin_client_signup_approval";i:0;s:18:"allowed_file_types";N;s:22:"google_calendar_status";s:8:"inactive";s:16:"google_client_id";N;s:20:"google_client_secret";N;s:35:"google_calendar_verification_status";s:12:"non_verified";s:9:"google_id";N;s:4:"name";N;s:5:"token";N;s:4:"hash";s:32:"9c6eabe1ea7d196a6ab33896fb8e475a";s:17:"allowed_file_size";i:10;s:20:"currency_key_version";s:4:"free";s:10:"created_at";s:19:"2025-08-26 07:59:46";s:10:"updated_at";s:19:"2025-09-07 10:05:23";s:10:"last_login";s:19:"2025-09-07 10:05:23";s:3:"rtl";i:0;s:9:"stripe_id";N;s:10:"card_brand";N;s:14:"card_last_four";N;s:13:"trial_ends_at";N;s:17:"licence_expire_on";N;s:18:"license_updated_at";N;s:23:"subscription_updated_at";N;s:8:"approved";i:1;s:11:"approved_by";N;s:22:"show_new_webhook_alert";i:0;s:7:"pm_type";N;s:12:"pm_last_four";N;s:24:"employee_can_export_data";i:1;s:7:"headers";s:797:"{
    "userAgent": "Symfony",
    "isMobile": false,
    "isTablet": false,
    "isDesktop": true,
    "isBot": false,
    "isChrome": false,
    "isFirefox": false,
    "isOpera": false,
    "isSafari": false,
    "isEdge": false,
    "isInApp": false,
    "isIE": false,
    "browserName": "",
    "browserFamily": "Unknown",
    "browserVersion": "",
    "browserVersionMajor": 0,
    "browserVersionMinor": 0,
    "browserVersionPatch": 0,
    "browserEngine": "Unknown",
    "platformName": "",
    "platformFamily": "Unknown",
    "platformVersion": "",
    "platformVersionMajor": 0,
    "platformVersionMinor": 0,
    "platformVersionPatch": 0,
    "isWindows": false,
    "isLinux": false,
    "isMac": false,
    "isAndroid": false,
    "deviceFamily": "Unknown",
    "deviceModel": ""
}";s:11:"register_ip";N;s:16:"location_details";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:22:"google_calendar_status";s:6:"string";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:4:{i:0;s:8:"logo_url";i:1;s:20:"login_background_url";i:2;s:18:"moment_date_format";i:3;s:11:"favicon_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:5:"dates";a:3:{i:0;s:10:"last_login";i:1;s:23:"subscription_updated_at";i:2;s:17:"licence_expire_on";}s:5:"model";N;s:13:"custom_fields";N;s:18:"custom_fields_data";N;s:17:"customerIpAddress";N;s:24:"estimationBillingAddress";a:0:{}s:13:"collectTaxIds";b:0;s:8:"couponId";N;s:15:"promotionCodeId";N;s:19:"allowPromotionCodes";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:4:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:7:"headers";i:3;s:16:"location_details";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:5:"dates";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:10:"last_login";}}}s:28:" * escapeWhenCastingToString";b:0;}s:12:"smtp_setting";O:22:"App\Models\SmtpSetting":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"smtp_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:14:{s:2:"id";i:1;s:11:"mail_driver";s:4:"smtp";s:9:"mail_host";s:14:"smtp.gmail.com";s:9:"mail_port";s:3:"465";s:13:"mail_username";s:17:"<EMAIL>";s:13:"mail_password";N;s:14:"mail_from_name";s:9:"Worksuite";s:15:"mail_from_email";s:14:"<EMAIL>";s:15:"mail_encryption";s:3:"ssl";s:14:"email_verified";i:0;s:8:"verified";i:0;s:15:"mail_connection";s:4:"sync";s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 08:06:16";}s:11:" * original";a:14:{s:2:"id";i:1;s:11:"mail_driver";s:4:"smtp";s:9:"mail_host";s:14:"smtp.gmail.com";s:9:"mail_port";s:3:"465";s:13:"mail_username";s:17:"<EMAIL>";s:13:"mail_password";N;s:14:"mail_from_name";s:9:"Worksuite";s:15:"mail_from_email";s:14:"<EMAIL>";s:15:"mail_encryption";s:3:"ssl";s:14:"email_verified";i:0;s:8:"verified";i:0;s:15:"mail_connection";s:4:"sync";s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 08:06:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:13:"mail_password";s:9:"encrypted";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"set_smtp_message";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}s:15:"pusher_settings";O:24:"App\Models\PusherSetting":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"pusher_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:13:"pusher_app_id";N;s:14:"pusher_app_key";N;s:17:"pusher_app_secret";N;s:14:"pusher_cluster";N;s:9:"force_tls";i:0;s:6:"status";i:0;s:9:"taskboard";i:1;s:8:"messages";i:0;s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 07:59:44";}s:11:" * original";a:11:{s:2:"id";i:1;s:13:"pusher_app_id";N;s:14:"pusher_app_key";N;s:17:"pusher_app_secret";N;s:14:"pusher_cluster";N;s:9:"force_tls";i:0;s:6:"status";i:0;s:9:"taskboard";i:1;s:8:"messages";i:0;s:10:"created_at";s:19:"2025-08-26 07:59:44";s:10:"updated_at";s:19:"2025-08-26 07:59:44";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}s:10:"user_roles";a:2:{i:0;s:5:"admin";i:1;s:8:"employee";}s:13:"user_role_ids";a:2:{i:0;i:1;i:1;i:2;}s:5:"isRtl";i:0;s:23:"add_timelogs_permission";s:3:"all";s:22:"add_project_permission";s:3:"all";s:20:"add_tasks_permission";s:3:"all";s:22:"add_clients_permission";s:3:"all";s:24:"add_employees_permission";s:3:"all";s:23:"add_payments_permission";s:3:"all";s:22:"add_tickets_permission";s:3:"all";s:15:"message_setting";O:25:"App\Models\MessageSetting":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"message_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1;s:10:"company_id";i:1;s:18:"allow_client_admin";s:2:"no";s:21:"allow_client_employee";s:2:"no";s:15:"restrict_client";s:2:"no";s:10:"created_at";s:19:"2025-08-26 07:59:47";s:10:"updated_at";s:19:"2025-08-26 07:59:47";s:23:"send_sound_notification";i:0;}s:11:" * original";a:8:{s:2:"id";i:1;s:10:"company_id";i:1;s:18:"allow_client_admin";s:2:"no";s:21:"allow_client_employee";s:2:"no";s:15:"restrict_client";s:2:"no";s:10:"created_at";s:19:"2025-08-26 07:59:47";s:10:"updated_at";s:19:"2025-08-26 07:59:47";s:23:"send_sound_notification";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}s:18:"attendance_setting";O:28:"App\Models\AttendanceSetting":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:19:"attendance_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:27:{s:2:"id";i:1;s:10:"company_id";i:1;s:13:"auto_clock_in";s:2:"no";s:22:"auto_clock_in_location";s:6:"office";s:17:"office_start_time";s:8:"09:00:00";s:15:"office_end_time";s:8:"18:00:00";s:17:"halfday_mark_time";s:8:"13:00:00";s:18:"late_mark_duration";i:20;s:14:"clockin_in_day";i:1;s:21:"employee_clock_in_out";s:3:"yes";s:16:"office_open_days";s:11:"[1,2,3,4,5]";s:10:"ip_address";N;s:6:"radius";N;s:12:"radius_check";s:2:"no";s:8:"ip_check";s:2:"no";s:11:"alert_after";N;s:18:"alert_after_status";i:0;s:21:"save_current_location";i:0;s:22:"default_employee_shift";i:2;s:15:"week_start_from";s:1:"1";s:18:"allow_shift_change";i:1;s:20:"show_clock_in_button";s:2:"no";s:10:"created_at";s:19:"2025-08-26 07:59:47";s:10:"updated_at";s:19:"2025-08-26 07:59:47";s:14:"monthly_report";i:0;s:20:"monthly_report_roles";N;s:9:"qr_enable";s:1:"1";}s:11:" * original";a:27:{s:2:"id";i:1;s:10:"company_id";i:1;s:13:"auto_clock_in";s:2:"no";s:22:"auto_clock_in_location";s:6:"office";s:17:"office_start_time";s:8:"09:00:00";s:15:"office_end_time";s:8:"18:00:00";s:17:"halfday_mark_time";s:8:"13:00:00";s:18:"late_mark_duration";i:20;s:14:"clockin_in_day";i:1;s:21:"employee_clock_in_out";s:3:"yes";s:16:"office_open_days";s:11:"[1,2,3,4,5]";s:10:"ip_address";N;s:6:"radius";N;s:12:"radius_check";s:2:"no";s:8:"ip_check";s:2:"no";s:11:"alert_after";N;s:18:"alert_after_status";i:0;s:21:"save_current_location";i:0;s:22:"default_employee_shift";i:2;s:15:"week_start_from";s:1:"1";s:18:"allow_shift_change";i:1;s:20:"show_clock_in_button";s:2:"no";s:10:"created_at";s:19:"2025-08-26 07:59:47";s:10:"updated_at";s:19:"2025-08-26 07:59:47";s:14:"monthly_report";i:0;s:20:"monthly_report_roles";N;s:9:"qr_enable";s:1:"1";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:5:"pivot";}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:9:"qr_enable";i:1;s:22:"default_employee_shift";}s:10:" * guarded";a:0:{}s:10:" * default";a:1:{i:0;s:2:"id";}s:13:" * filterable";a:1:{i:0;s:2:"id";}s:21:" * relationAttributes";a:0:{}s:6:" * raw";a:0:{}s:8:" * dates";a:0:{}}}